{"name": "@externals/devDependencies", "description": "External dev dependencies - put dev build dependencies here", "version": "3.11.0-beta.44", "license": "MIT", "private": true, "engines": {"node": ">=12", "yarn": ">=1.19.1"}, "dependencies": {"@babel/runtime": "^7.20.13", "@kitware/vtk.js": "32.12.0", "clsx": "^2.1.1", "core-js": "^3.2.1", "moment": "^2.30.1"}, "peerDependencies": {"react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@pmmmwh/react-refresh-webpack-plugin": "^0.5.15", "@rsbuild/core": "^1.3.16", "@rsbuild/plugin-node-polyfill": "^1.3.0", "@rsbuild/plugin-react": "^1.3.1", "@svgr/webpack": "^8.1.0", "@swc/helpers": "^0.5.15", "@types/jest": "^27.5.0", "@typescript-eslint/eslint-plugin": "^6.3.0", "@typescript-eslint/parser": "^6.3.0", "autoprefixer": "^10.4.4", "babel-eslint": "9.x", "babel-loader": "^8.2.4", "babel-plugin-module-resolver": "^5.0.0", "clean-webpack-plugin": "^3.0.0", "copy-webpack-plugin": "^9.0.1", "cross-env": "^5.2.0", "css-loader": "^6.8.1", "dotenv": "^8.1.0", "eslint": "^8.39.0", "eslint-config-prettier": "^7.2.0", "eslint-config-react-app": "^6.0.0", "eslint-plugin-cypress": "^2.12.1", "eslint-plugin-flowtype": "^7.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-promise": "^5.2.0", "eslint-plugin-react": "^7.29.4", "eslint-plugin-react-hooks": "^4.4.0", "eslint-plugin-tsdoc": "^0.2.11", "eslint-webpack-plugin": "^2.5.3", "execa": "^8.0.1", "extract-css-chunks-webpack-plugin": "^4.5.4", "html-webpack-plugin": "^5.3.2", "husky": "^3.0.0", "jest": "^29.5.0", "jest-canvas-mock": "^2.1.0", "jest-environment-jsdom": "^29.5.0", "jest-junit": "^6.4.0", "lerna": "^7.2.0", "lint-staged": "^9.0.2", "mini-css-extract-plugin": "^2.1.0", "optimize-css-assets-webpack-plugin": "^6.0.1", "postcss": "^8.3.5", "postcss-import": "^14.0.2", "postcss-loader": "^6.1.1", "postcss-preset-env": "^7.4.3", "prettier": "^3.0.3", "prettier-plugin-tailwindcss": "^0.5.4", "react-refresh": "^0.14.2", "semver": "^7.5.1", "serve": "^14.2.4", "shader-loader": "^1.3.1", "shx": "^0.3.3", "source-map-loader": "^4.0.1", "style-loader": "^1.0.0", "stylus": "^0.59.0", "stylus-loader": "^7.1.3", "terser-webpack-plugin": "^5.1.4", "typescript": "5.5.4", "unused-webpack-plugin": "2.4.0", "webpack": "5.94.0", "webpack-bundle-analyzer": "^4.8.0", "webpack-cli": "^4.7.2", "webpack-dev-server": "4.7.3", "webpack-hot-middleware": "^2.25.0", "webpack-merge": "^5.7.3", "workbox-webpack-plugin": "^6.1.5", "worker-loader": "^3.0.8"}, "scripts": {"build": "Included as direct dependency"}}