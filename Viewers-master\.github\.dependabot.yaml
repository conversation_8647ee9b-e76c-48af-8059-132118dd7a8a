version: 2
enable-beta-ecosystems: true
updates:
  - package-ecosystem: 'bun'
    directory: '/'
    schedule:
      interval: 'daily'
    labels: ['dependencies']
    commit-message:
      prefix: 'chore'
      include: 'scope'
  - package-ecosystem: 'npm'
    directory: '/'
    schedule:
      interval: 'daily'
    labels: ['dependencies']
    commit-message:
      prefix: 'chore'
      include: 'scope'
