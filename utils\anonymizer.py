import pydicom
import os
import logging
from datetime import datetime

class DicomAnonymizer:
    """Handle DICOM file anonymization by removing patient identifiers"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Tags to remove or modify for anonymization
        self.tags_to_remove = [
            'PatientName',
            'PatientID',
            'PatientBirthDate',
            'PatientSex',
            'PatientAge',
            'PatientWeight',
            'PatientAddress',
            'PatientTelephoneNumbers',
            'PatientComments',
            'ReferringPhysicianName',
            'PerformingPhysicianName',
            'OperatorsName',
            'RequestingPhysician',
            'InstitutionAddress',
            'StationName',
            'AccessionNumber',
            'StudyID',
            'RequestedProcedureDescription',
            'ScheduledProcedureStepDescription',
        ]
        
        # Tags to modify (not remove completely)
        self.tags_to_modify = {
            'StudyDate': lambda x: '',  # Remove study date
            'SeriesDate': lambda x: '',
            'AcquisitionDate': lambda x: '',
            'ContentDate': lambda x: '',
            'StudyTime': lambda x: '',
            'SeriesTime': lambda x: '',
            'AcquisitionTime': lambda x: '',
            'ContentTime': lambda x: '',
        }
    
    def anonymize_file(self, filepath):
        """Anonymize DICOM file and save as new file"""
        try:
            # Ensure we're using absolute path
            filepath = os.path.abspath(filepath)
            self.logger.info(f'Anonymizing file: {filepath}')
            
            if not os.path.exists(filepath):
                self.logger.error(f'Source file not found: {filepath}')
                raise FileNotFoundError(f'Source file not found: {filepath}')
            
            # Read DICOM file
            ds = pydicom.dcmread(filepath)
            
            # Remove patient identifiers
            for tag in self.tags_to_remove:
                if hasattr(ds, tag):
                    delattr(ds, tag)
            
            # Modify specific tags
            for tag, modifier in self.tags_to_modify.items():
                if hasattr(ds, tag):
                    setattr(ds, tag, modifier(getattr(ds, tag)))
            
            # Add anonymization markers
            ds.PatientName = "ANONYMOUS"
            ds.PatientID = "ANON_ID"
            
            # Save anonymized file
            output_dir = os.path.dirname(filepath)
            output_filename = f'anon_{os.path.basename(filepath)}'
            output_path = os.path.join(output_dir, output_filename)
            
            self.logger.info(f'Saving anonymized file to: {output_path}')
            ds.save_as(output_path)
            
            if not os.path.exists(output_path):
                self.logger.error(f'Failed to save anonymized file: {output_path}')
                raise IOError(f'Failed to save anonymized file: {output_path}')
            
            self.logger.info(f'Successfully saved anonymized file: {output_path}')
            return output_path
            
        except Exception as e:
            self.logger.error(f"Anonymization failed: {str(e)}")
            raise e
    
    def verify_anonymization(self, filepath):
        """Verify that a DICOM file has been properly anonymized"""
        try:
            ds = pydicom.dcmread(filepath)
            
            # Check for presence of sensitive tags
            found_sensitive = []
            for tag in self.tags_to_remove:
                if hasattr(ds, tag):
                    value = getattr(ds, tag)
                    if value and str(value).strip():  # Non-empty value
                        found_sensitive.append(tag)
            
            if found_sensitive:
                self.logger.warning(f"Sensitive data found in anonymized file: {found_sensitive}")
                return False, found_sensitive
            
            return True, []
            
        except Exception as e:
            self.logger.error(f"Anonymization verification failed: {str(e)}")
            return False, [str(e)]
