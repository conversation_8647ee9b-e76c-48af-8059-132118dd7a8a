class DicomViewer {
    constructor(containerId, studyUid) {
        this.container = document.getElementById(containerId);
        this.studyUid = studyUid;
        this.imageData = null;
        this.canvas = null;
        this.ctx = null;
        this.isLoading = true;
        
        this.init();
    }
    
    init() {
        this.setupViewer();
        this.loadDicomData();
    }
    
    setupViewer() {
        // Create viewer container
        this.container.innerHTML = `
            <div class="dicom-viewer">
                <div id="loading-spinner" class="text-center p-4">
                    <div class="spinner"></div>
                    <p>Loading DICOM image...</p>
                </div>
                <canvas id="dicom-canvas" style="display: none; max-width: 100%; height: auto;"></canvas>
                <div class="viewer-controls" id="viewer-controls" style="display: none;">
                    <div class="row">
                        <div class="col-md-6">
                            <label for="brightness">Brightness:</label>
                            <input type="range" id="brightness" min="-100" max="100" value="0" class="form-range">
                        </div>
                        <div class="col-md-6">
                            <label for="contrast">Contrast:</label>
                            <input type="range" id="contrast" min="0" max="200" value="100" class="form-range">
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-12">
                            <button class="btn btn-sm btn-secondary" onclick="dicomViewer.resetImage()">Reset</button>
                            <button class="btn btn-sm btn-primary" onclick="dicomViewer.downloadImage()">Download</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        this.canvas = document.getElementById('dicom-canvas');
        this.ctx = this.canvas.getContext('2d');
        
        // Setup event listeners
        document.getElementById('brightness').addEventListener('input', () => this.updateImage());
        document.getElementById('contrast').addEventListener('input', () => this.updateImage());
    }
    
    async loadDicomData() {
        try {
            const response = await fetch(`/api/dicom-data/${this.studyUid}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            this.imageData = await response.json();
            this.displayImage();
            this.hideLoading();
            
        } catch (error) {
            console.error('Error loading DICOM data:', error);
            this.showError('Failed to load DICOM image: ' + error.message);
        }
    }
    
    displayImage() {
        if (!this.imageData || !this.imageData.image) {
            this.showError('No image data available');
            return;
        }
        
        const img = new Image();
        img.onload = () => {
            // Set canvas size to match image
            this.canvas.width = this.imageData.width;
            this.canvas.height = this.imageData.height;
            
            // Draw original image
            this.originalImageData = img;
            this.ctx.drawImage(img, 0, 0);
            
            // Show canvas and controls
            this.canvas.style.display = 'block';
            document.getElementById('viewer-controls').style.display = 'block';
        };
        
        img.onerror = () => {
            this.showError('Failed to load image data');
        };
        
        img.src = this.imageData.image;
    }
    
    updateImage() {
        if (!this.originalImageData) return;
        
        const brightness = parseInt(document.getElementById('brightness').value);
        const contrast = parseInt(document.getElementById('contrast').value);
        
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Apply filters
        this.ctx.filter = `brightness(${100 + brightness}%) contrast(${contrast}%)`;
        this.ctx.drawImage(this.originalImageData, 0, 0);
        this.ctx.filter = 'none';
    }
    
    resetImage() {
        document.getElementById('brightness').value = 0;
        document.getElementById('contrast').value = 100;
        this.updateImage();
    }
    
    downloadImage() {
        if (!this.canvas) return;
        
        const link = document.createElement('a');
        link.download = `dicom_${this.studyUid}.png`;
        link.href = this.canvas.toDataURL();
        link.click();
    }
    
    hideLoading() {
        const spinner = document.getElementById('loading-spinner');
        if (spinner) {
            spinner.style.display = 'none';
        }
        this.isLoading = false;
    }
    
    showError(message) {
        this.container.innerHTML = `
            <div class="alert alert-danger">
                <h5>Error Loading DICOM Image</h5>
                <p>${message}</p>
                <button class="btn btn-primary" onclick="location.reload()">Retry</button>
            </div>
        `;
    }
}

// Global viewer instance
let dicomViewer = null;

// Initialize viewer when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    const viewerContainer = document.getElementById('dicom-viewer-container');
    if (viewerContainer) {
        const studyUid = viewerContainer.dataset.studyUid;
        dicomViewer = new DicomViewer('dicom-viewer-container', studyUid);
    }
});
