{% extends "base.html" %}

{% block title %}DICOM Viewer - {{ study.study_description or 'Study' }}{% endblock %}

{% block head %}
<!-- Cornerstone Dependencies -->
<script src="https://unpkg.com/cornerstone-core/dist/cornerstone.min.js"></script>
<script src="https://unpkg.com/cornerstone-math/dist/cornerstoneMath.min.js"></script>
<script src="https://unpkg.com/cornerstone-tools/dist/cornerstoneTools.min.js"></script>
<script src="https://unpkg.com/dicom-parser/dist/dicomParser.min.js"></script>
<script src="https://unpkg.com/cornerstone-wado-image-loader/dist/cornerstoneWADOImageLoader.min.js"></script>

<!-- Custom Viewer Integration -->
<script src="{{ url_for('static', filename='js/dicom-viewer.js') }}"></script>

<style>
    .cornerstone-canvas { width: 100% !important; height: 600px !important; }
    .viewer-tools { padding: 10px; background: var(--card-bg); border-top: 1px solid var(--border); }
    .viewer-tools button { margin-right: 5px; }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2>
                    <i data-feather="monitor" class="me-2"></i>
                    DICOM Viewer
                </h2>
                <p class="text-muted mb-0">{{ study.study_description or study.original_filename }}</p>
            </div>
            <div class="btn-group">
                {% if not is_shared %}
                <a href="{{ url_for('share_study', study_uid=study.study_uid) }}" class="btn btn-outline-primary">
                    <i data-feather="share-2" class="me-1"></i>
                    Share
                </a>
                <a href="{{ url_for('download_study', study_uid=study.study_uid) }}" class="btn btn-outline-secondary">
                    <i data-feather="download" class="me-1"></i>
                    Download
                </a>
                {% endif %}
                <a href="{{ url_for('index') }}" class="btn btn-secondary">
                    <i data-feather="arrow-left" class="me-1"></i>
                    Back
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- DICOM Viewer -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i data-feather="image" class="me-2"></i>
                    Medical Image
                </h5>
            </div>
            <div class="card-body p-0">
                <div id="dicom-viewer-container" style="height: 600px; width: 100%;">
                    <div id="dicom-image" data-study-uid="{{ study.study_uid }}" style="width: 100%; height: 100%;"></div>
                    <div class="viewer-tools">
                        <button class="btn btn-sm btn-primary" onclick="window.viewer.resetImage()">Reset View</button>
                        <button class="btn btn-sm btn-success" onclick="window.viewer.toggleWindowLevel()">Window/Level</button>
                        <button class="btn btn-sm btn-info" onclick="window.viewer.togglePan()">Pan</button>
                        <button class="btn btn-sm btn-warning" onclick="window.viewer.toggleZoom()">Zoom</button>
                        <button class="btn btn-sm btn-secondary" onclick="window.viewer.invertImage()">Invert</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Study Information -->
    <div class="col-lg-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i data-feather="info" class="me-2"></i>
                    Study Information
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-sm metadata-table">
                    <tbody>
                        <tr>
                            <th>Study ID</th>
                            <td><code>{{ study.study_uid[:8] }}...</code></td>
                        </tr>
                        <tr>
                            <th>Upload Date</th>
                            <td>{{ study.upload_date.strftime('%Y-%m-%d %H:%M') }}</td>
                        </tr>
                        <tr>
                            <th>File Size</th>
                            <td>{{ "%.2f"|format(study.file_size / 1024 / 1024) }} MB</td>
                        </tr>
                        <tr>
                            <th>Views</th>
                            <td>{{ study.view_count }}</td>
                        </tr>
                        {% if study.last_viewed %}
                        <tr>
                            <th>Last Viewed</th>
                            <td>{{ study.last_viewed.strftime('%Y-%m-%d %H:%M') }}</td>
                        </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- DICOM Metadata -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i data-feather="file-text" class="me-2"></i>
                    DICOM Metadata
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-sm metadata-table">
                    <tbody>
                        {% if study.modality %}
                        <tr>
                            <th>Modality</th>
                            <td><span class="badge badge-secondary">{{ study.modality }}</span></td>
                        </tr>
                        {% endif %}
                        {% if study.study_date %}
                        <tr>
                            <th>Study Date</th>
                            <td>{{ study.study_date }}</td>
                        </tr>
                        {% endif %}
                        {% if study.study_description %}
                        <tr>
                            <th>Description</th>
                            <td>{{ study.study_description }}</td>
                        </tr>
                        {% endif %}
                        {% if study.institution_name %}
                        <tr>
                            <th>Institution</th>
                            <td>{{ study.institution_name }}</td>
                        </tr>
                        {% endif %}
                        {% if study.manufacturer %}
                        <tr>
                            <th>Manufacturer</th>
                            <td>{{ study.manufacturer }}</td>
                        </tr>
                        {% endif %}
                        <tr>
                            <th>Status</th>
                            <td>
                                {% if study.is_shared %}
                                    <span class="badge badge-success">
                                        <i data-feather="share-2" style="width: 12px; height: 12px;"></i>
                                        Shared
                                    </span>
                                {% else %}
                                    <span class="badge badge-secondary">Private</span>
                                {% endif %}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Quick Share Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i data-feather="share-2" class="me-2"></i>
                    Quick Share
                </h5>
            </div>
            <div class="card-body">
                {% if study.is_shared and study.share_token %}
                    <!-- Already Shared - Show Quick Access -->
                    <div class="alert alert-success mb-3">
                        <i data-feather="check-circle" class="me-2"></i>
                        Study is shared publicly
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-semibold">Share Link</label>
                        <div class="input-group">
                            <input type="text"
                                   class="form-control"
                                   id="quick-share-url"
                                   value="{{ url_for('shared_viewer', token=study.share_token, _external=True) }}"
                                   readonly>
                            <button class="btn btn-outline-primary copy-to-clipboard"
                                    data-target="quick-share-url">
                                <i data-feather="copy" class="me-1"></i>
                                Copy
                            </button>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-6">
                            <a href="{{ url_for('shared_viewer', token=study.share_token) }}"
                               class="btn btn-sm btn-success w-100"
                               target="_blank">
                                <i data-feather="external-link" class="me-1"></i>
                                Test Link
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="{{ url_for('share_study', study_uid=study.study_uid) }}"
                               class="btn btn-sm btn-outline-secondary w-100">
                                <i data-feather="qr-code" class="me-1"></i>
                                Get QR Code
                            </a>
                        </div>
                    </div>

                    {% if study.share_expires %}
                    <small class="text-muted mt-2 d-block">
                        Expires: {{ study.share_expires.strftime('%Y-%m-%d %H:%M') }}
                    </small>
                    {% endif %}
                {% else %}
                    <!-- Not Shared - Quick Generate -->
                    <p class="text-muted mb-3">Generate a shareable link to present this study</p>

                    <form method="POST" action="{{ url_for('share_study', study_uid=study.study_uid) }}" class="mb-3">
                        <div class="row align-items-end">
                            <div class="col-8">
                                <label class="form-label">Expires in:</label>
                                <select name="expiry_days" class="form-select form-select-sm">
                                    <option value="1">1 Day</option>
                                    <option value="7">7 Days</option>
                                    <option value="30" selected>30 Days</option>
                                    <option value="90">90 Days</option>
                                </select>
                            </div>
                            <div class="col-4">
                                <button type="submit" class="btn btn-primary btn-sm w-100">
                                    <i data-feather="share-2" class="me-1"></i>
                                    Share
                                </button>
                            </div>
                        </div>
                    </form>

                    <div class="alert alert-info">
                        <i data-feather="shield" class="me-2"></i>
                        <small>Study will be automatically anonymized before sharing</small>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Sharing Information -->
        {% if study.is_shared and study.share_token %}
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i data-feather="bar-chart-2" class="me-2"></i>
                    Sharing Stats
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="border rounded p-2">
                            <h6 class="text-primary mb-1">{{ study.view_count }}</h6>
                            <small class="text-muted">Views</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border rounded p-2">
                            <h6 class="text-success mb-1">Active</h6>
                            <small class="text-muted">Status</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border rounded p-2">
                            <h6 class="text-info mb-1">
                                {% if study.share_expires %}
                                    {{ (study.share_expires - now).days if (study.share_expires - now).total_seconds() > 0 else 0 }}d
                                {% else %}
                                    ∞
                                {% endif %}
                            </h6>
                            <small class="text-muted">Left</small>
                        </div>
                    </div>
                </div>

                <div class="d-grid mt-3">
                    <a href="{{ url_for('share_study', study_uid=study.study_uid) }}" class="btn btn-outline-primary btn-sm">
                        <i data-feather="settings" class="me-1"></i>
                        Advanced Sharing Options & QR Code
                    </a>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Patient Information & Report -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i data-feather="user" class="me-2"></i>
                    Patient Information & Report
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('update_report', study_uid=study.study_uid) }}">
                    <!-- Patient Information -->
                    <h6 class="text-primary mb-3">Patient Information</h6>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label class="form-label">Age</label>
                            <input type="text" class="form-control" name="patient_age"
                                   value="{{ study.patient_age or '' }}" placeholder="e.g., 45">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Gender</label>
                            <select class="form-select" name="patient_gender">
                                <option value="">Select...</option>
                                <option value="Male" {{ 'selected' if study.patient_gender == 'Male' }}>Male</option>
                                <option value="Female" {{ 'selected' if study.patient_gender == 'Female' }}>Female</option>
                                <option value="Other" {{ 'selected' if study.patient_gender == 'Other' }}>Other</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Referring Physician</label>
                            <input type="text" class="form-control" name="referring_physician"
                                   value="{{ study.referring_physician or '' }}" placeholder="Dr. Name">
                        </div>
                    </div>

                    <!-- Clinical Report -->
                    <h6 class="text-primary mb-3">Clinical Report</h6>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Diagnosis</label>
                            <textarea class="form-control" name="diagnosis" rows="3"
                                      placeholder="Primary diagnosis...">{{ study.diagnosis or '' }}</textarea>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Findings</label>
                            <textarea class="form-control" name="findings" rows="3"
                                      placeholder="Key findings and observations...">{{ study.findings or '' }}</textarea>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Impression</label>
                            <textarea class="form-control" name="impression" rows="3"
                                      placeholder="Clinical impression...">{{ study.impression or '' }}</textarea>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Recommendations</label>
                            <textarea class="form-control" name="recommendations" rows="3"
                                      placeholder="Treatment recommendations...">{{ study.recommendations or '' }}</textarea>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Reporting Physician</label>
                            <input type="text" class="form-control" name="reporting_physician"
                                   value="{{ study.reporting_physician or '' }}" placeholder="Dr. Name">
                        </div>
                        <div class="col-md-6">
                            {% if study.report_date %}
                            <label class="form-label">Last Updated</label>
                            <input type="text" class="form-control"
                                   value="{{ study.report_date.strftime('%Y-%m-%d %H:%M') }}" readonly>
                            {% endif %}
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i data-feather="save" class="me-1"></i>
                            Save Report
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Additional Study Details -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i data-feather="activity" class="me-2"></i>
                    Technical Details
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>File Information</h6>
                        <ul class="list-unstyled">
                            <li><strong>Original Filename:</strong> {{ study.original_filename }}</li>
                            <li><strong>Anonymized File:</strong> {{ study.anonymized_filename }}</li>
                            <li><strong>File Size:</strong> {{ "%.2f"|format(study.file_size / 1024 / 1024) }} MB</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Processing Status</h6>
                        <ul class="list-unstyled">
                            <li>
                                <i data-feather="check" class="text-success me-1" style="width: 16px; height: 16px;"></i>
                                DICOM validation passed
                            </li>
                            <li>
                                <i data-feather="check" class="text-success me-1" style="width: 16px; height: 16px;"></i>
                                Patient data anonymized
                            </li>
                            <li>
                                <i data-feather="check" class="text-success me-1" style="width: 16px; height: 16px;"></i>
                                Image data extracted
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Use the Cornerstone-based viewer only -->
{% endblock %}
