.ViewportOrientationMarkers {
  --marker-width: 100px;
  --marker-height: 100px;
  --scrollbar-width: 20px;
  pointer-events: none;
  line-height: 18px;
}
.ViewportOrientationMarkers .orientation-marker {
  position: absolute;
}
.ViewportOrientationMarkers .top-mid {
  top: 0.38rem;
  left: 50%;
  transform: translateX(-50%);
}
.ViewportOrientationMarkers .left-mid {
  top: 50%;
  left: 0.38rem;
  transform: translateY(-50%);
}
.ViewportOrientationMarkers .right-mid {
  top: 50%;
  left: calc(100% - var(--marker-width) - var(--scrollbar-width));
  transform: translateY(-50%);
}
.ViewportOrientationMarkers .bottom-mid {
  top: calc(100% - var(--marker-height) - 0.6rem);
  left: 50%;
  transform: translateX(-50%);
}
.ViewportOrientationMarkers .right-mid .orientation-marker-value {
  display: flex;
  justify-content: flex-end;
  min-width: var(--marker-width);
}
.ViewportOrientationMarkers .bottom-mid .orientation-marker-value {
  display: flex;
  justify-content: flex-start;
  min-height: var(--marker-height);
  flex-direction: column-reverse;
}
