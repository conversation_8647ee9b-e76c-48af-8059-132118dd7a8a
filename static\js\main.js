// Main JavaScript functionality for the DICOM platform

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // File upload drag and drop functionality
    setupFileUpload();
    
    // Copy to clipboard functionality
    setupCopyToClipboard();
    
    // Confirmation dialogs
    setupConfirmationDialogs();
});

function setupFileUpload() {
    const uploadArea = document.getElementById('upload-area');
    const fileInput = document.getElementById('file-input');
    
    if (!uploadArea || !fileInput) return;
    
    // Drag and drop events
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            updateFileDisplay(files[0]);
        }
    });
    
    // Click to upload
    uploadArea.addEventListener('click', function() {
        fileInput.click();
    });
    
    // File input change
    fileInput.addEventListener('change', function() {
        if (this.files.length > 0) {
            updateFileDisplay(this.files[0]);
        }
    });
}

function updateFileDisplay(file) {
    const uploadArea = document.getElementById('upload-area');
    const fileName = file.name;
    const fileSize = formatFileSize(file.size);
    
    uploadArea.innerHTML = `
        <div class="upload-icon">
            <i data-feather="check-circle"></i>
        </div>
        <h4>File Selected</h4>
        <p><strong>${fileName}</strong></p>
        <p>Size: ${fileSize}</p>
        <p class="text-muted">Click submit to upload</p>
    `;
    
    // Reinitialize feather icons
    if (typeof feather !== 'undefined') {
        feather.replace();
    }
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function setupCopyToClipboard() {
    const copyButtons = document.querySelectorAll('.copy-to-clipboard');
    
    copyButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetId = this.dataset.target;
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                // Select text
                targetElement.select();
                targetElement.setSelectionRange(0, 99999); // For mobile
                
                // Copy to clipboard
                document.execCommand('copy');
                
                // Update button text
                const originalText = this.textContent;
                this.textContent = 'Copied!';
                this.classList.add('btn-success');
                
                setTimeout(() => {
                    this.textContent = originalText;
                    this.classList.remove('btn-success');
                }, 2000);
            }
        });
    });
}

function setupConfirmationDialogs() {
    const deleteButtons = document.querySelectorAll('.delete-confirm');
    
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            const studyName = this.dataset.studyName || 'this study';
            const confirmed = confirm(`Are you sure you want to delete ${studyName}? This action cannot be undone.`);
            
            if (!confirmed) {
                e.preventDefault();
            }
        });
    });
}

// Utility functions
function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alert-container') || document.body;
    
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show`;
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.appendChild(alert);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 5000);
}

function formatDate(dateString) {
    if (!dateString) return 'N/A';
    
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    } catch (e) {
        return dateString;
    }
}

// Share link functionality
function generateShareLink(studyUid) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `/share/${studyUid}`;
    
    const expiryInput = document.createElement('input');
    expiryInput.type = 'hidden';
    expiryInput.name = 'expiry_days';
    expiryInput.value = document.getElementById('expiry-days').value || '30';
    
    form.appendChild(expiryInput);
    document.body.appendChild(form);
    form.submit();
}

// Progress bar for uploads
function updateProgress(percent) {
    const progressBar = document.querySelector('.progress-bar');
    if (progressBar) {
        progressBar.style.width = percent + '%';
        progressBar.setAttribute('aria-valuenow', percent);
        progressBar.textContent = percent + '%';
    }
}

// Search functionality for admin panel
function filterStudies(searchTerm) {
    const tableRows = document.querySelectorAll('#studies-table tbody tr');
    
    tableRows.forEach(row => {
        const text = row.textContent.toLowerCase();
        const match = text.includes(searchTerm.toLowerCase());
        row.style.display = match ? '' : 'none';
    });
}

// Initialize search if search input exists
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('study-search');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            filterStudies(this.value);
        });
    }
});
