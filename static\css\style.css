:root {
  /* Black and Green color palette */
  --primary: #00FF00; /* bright green */
  --secondary: #1a1a1a; /* dark gray */
  --background: #000000; /* black */
  --text: #ffffff; /* white */
  --accent: #32CD32; /* lime green */
  --success: #00FF00; /* bright green */
  --error: #ff3333; /* red */
  --border: #333333; /* dark gray */
  --card-bg: #1a1a1a; /* slightly lighter black */
  
  /* Spacing */
  --spacing-unit: 16px;
  --border-radius: 4px;
}

/* Base styles */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'SF Pro Display', system-ui, sans-serif;
  background-color: var(--background);
  color: var(--text);
  line-height: 1.6;
  margin: 0;
  padding: 0;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  margin-bottom: var(--spacing-unit);
  color: hsl(var(--success));
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }

p {
  margin-bottom: var(--spacing-unit);
}

/* Navigation */
.navbar {
  background-color: var(--secondary) !important;
  border-bottom: 2px solid var(--primary);
  padding: var(--spacing-unit) 0;
}

.navbar-brand {
  font-weight: 700;
  font-size: 1.5rem;
  color: var(--primary) !important;
}

.navbar-nav .nav-link {
  color: var(--text) !important;
  font-weight: 500;
  padding: 0.5rem 1rem !important;
  border-radius: var(--border-radius);
  transition: all 0.2s ease;
}

.navbar-nav .nav-link:hover {
  color: var(--primary) !important;
  background-color: var(--primary);
  color: black;
  color: hsl(var(--primary)) !important;
  background-color: rgba(0, 255, 0, 0.1);
}

.navbar-nav .nav-link.active {
  background-color: hsl(var(--primary));
  color: black !important;
}

/* Cards */
.card {
  background-color: var(--card-bg);
  border: 1px solid var(--border);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-unit);
}

.card:hover {
  box-shadow: var(--shadow-lg);
}

.card-header {
  background-color: hsl(var(--border));
  border-bottom: 1px solid hsl(var(--primary));
  font-weight: 600;
  color: hsl(var(--primary));
  padding: var(--spacing-unit);
}

.card-body {
  padding: var(--spacing-unit);
}

/* Buttons */
.btn {
  border-radius: var(--border-radius);
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  border: none;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-block;
  cursor: pointer;
}

.btn-primary {
  background-color: hsl(var(--primary));
  color: white;
}

.btn-primary:hover {
  background-color: hsl(var(--primary) / 0.9);
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: hsl(var(--secondary));
  color: white;
}

.btn-secondary:hover {
  background-color: hsl(var(--secondary) / 0.9);
}

.btn-success {
  background-color: hsl(var(--success));
  color: white;
}

.btn-success:hover {
  background-color: hsl(var(--success) / 0.9);
}

.btn-warning {
  background-color: hsl(var(--warning));
  color: white;
}

.btn-warning:hover {
  background-color: hsl(var(--warning) / 0.9);
}

.btn-danger {
  background-color: hsl(var(--error));
  color: white;
}

.btn-danger:hover {
  background-color: hsl(var(--error) / 0.9);
}

.btn-outline-primary {
  border: 2px solid hsl(var(--primary));
  color: hsl(var(--primary));
  background-color: transparent;
}

.btn-outline-primary:hover {
  background-color: hsl(var(--primary));
  color: white;
}

/* Forms */
.form-control {
  border: 2px solid hsl(var(--border));
  border-radius: var(--border-radius);
  padding: 0.75rem 1rem;
  transition: border-color 0.2s ease;
  background-color: hsl(var(--card-bg));
  color: hsl(var(--text));
}

.form-control:focus {
  border-color: hsl(var(--primary));
  box-shadow: 0 0 0 3px hsl(var(--primary) / 0.2);
  outline: none;
  background-color: hsl(var(--card-bg));
}

.form-label {
  font-weight: 600;
  color: hsl(var(--text));
  margin-bottom: 0.5rem;
}

/* Alerts */
.alert {
  border-radius: var(--border-radius);
  padding: var(--spacing-unit);
  margin-bottom: var(--spacing-unit);
  border: none;
}

.alert-success {
  background-color: hsl(var(--success) / 0.1);
  color: hsl(var(--success));
  border-left: 4px solid hsl(var(--success));
}

.alert-danger {
  background-color: hsl(var(--error) / 0.1);
  color: hsl(var(--error));
  border-left: 4px solid hsl(var(--error));
}

.alert-warning {
  background-color: hsl(var(--warning) / 0.1);
  color: hsl(var(--warning));
  border-left: 4px solid hsl(var(--warning));
}

.alert-info {
  background-color: hsl(var(--accent) / 0.1);
  color: hsl(var(--accent));
  border-left: 4px solid hsl(var(--accent));
}

/* Statistics cards */
.stats-card {
  text-align: center;
  padding: 2rem 1rem;
  background-color: var(--card-bg);
  border: 2px solid var(--primary);
  color: hsl(var(--primary));
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-unit);
}

.stats-number {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.stats-label {
  font-size: 1.1rem;
  opacity: 0.9;
}

/* File upload area */
.upload-area {
  border: 3px dashed hsl(var(--border));
  border-radius: var(--border-radius);
  padding: 3rem 2rem;
  text-align: center;
  background-color: hsl(var(--card-bg));
  transition: all 0.2s ease;
  cursor: pointer;
}

.upload-area:hover {
  border-color: hsl(var(--primary));
  background-color: hsl(var(--primary) / 0.05);
}

.upload-area.dragover {
  border-color: hsl(var(--primary));
  background-color: hsl(var(--primary) / 0.1);
}

.upload-icon {
  font-size: 3rem;
  color: hsl(var(--primary));
  margin-bottom: var(--spacing-unit);
}

/* DICOM viewer */
.dicom-viewer {
  background-color: black;
  border-radius: var(--border-radius);
  overflow: hidden;
  position: relative;
}

.dicom-image {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 0 auto;
}

.viewer-controls {
  position: absolute;
  bottom: var(--spacing-unit);
  left: var(--spacing-unit);
  right: var(--spacing-unit);
  background-color: rgba(0, 0, 0, 0.8);
  padding: var(--spacing-unit);
  border-radius: var(--border-radius);
  color: white;
}

.metadata-table {
  font-size: 0.9rem;
}

.metadata-table th {
  background-color: hsl(var(--border));
  color: hsl(var(--primary));
  font-weight: 600;
  width: 30%;
}

.metadata-table td {
  background-color: hsl(var(--card-bg));
  color: hsl(var(--text));
}

/* QR Code display */
.qr-code-container {
  text-align: center;
  padding: 2rem;
  background-color: hsl(var(--card-bg));
  border: 1px solid hsl(var(--border));
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
}

.qr-code-container img {
  max-width: 200px;
  height: auto;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }
  
  .stats-card {
    margin-bottom: 1rem;
  }
  
  .card-body {
    padding: 1rem;
  }
  
  h1 { font-size: 2rem; }
  h2 { font-size: 1.5rem; }
  
  .upload-area {
    padding: 2rem 1rem;
  }
}

/* Loading spinner */
.spinner {
  border: 4px solid hsl(var(--border));
  border-top: 4px solid hsl(var(--accent));
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 2rem auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Study list table */
.table {
  background-color: hsl(var(--card-bg));
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow);
}

.table th {
  background-color: hsl(var(--primary));
  color: black;
  font-weight: 600;
  border: none;
  padding: 1rem;
}

.table td {
  padding: 1rem;
  border-top: 1px solid hsl(var(--border));
  vertical-align: middle;
  color: hsl(var(--text));
  background-color: hsl(var(--card-bg));
}

.table tbody tr:hover {
  background-color: hsl(var(--border));
}

/* Badge styles */
.badge {
  padding: 0.5rem 0.75rem;
  border-radius: 2rem;
  font-size: 0.8rem;
  font-weight: 600;
}

.badge-success {
  background-color: hsl(var(--success));
  color: white;
}

.badge-secondary {
  background-color: hsl(var(--secondary));
  color: white;
}

/* Action buttons in tables */
.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
  margin: 0 0.25rem;
}

/* Footer */
.footer {
  background-color: hsl(var(--background));
  border-top: 2px solid hsl(var(--primary));
  color: hsl(var(--text));
  padding: 2rem 0;
  margin-top: 3rem;
}

.footer a {
  color: hsl(var(--primary));
  text-decoration: none;
}

.footer a:hover {
  color: hsl(var(--accent));
}
