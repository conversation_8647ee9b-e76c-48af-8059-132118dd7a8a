{% extends "base.html" %}

{% block title %}Dashboard - DICOM Platform{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i data-feather="activity" class="me-2"></i>
                Medical Imaging Dashboard
            </h1>
            <a href="{{ url_for('upload_file') }}" class="btn btn-primary">
                <i data-feather="upload" class="me-1"></i>
                Upload DICOM
            </a>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="stats-card">
            <div class="stats-number">{{ total_studies }}</div>
            <div class="stats-label">Total Studies</div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="stats-card">
            <div class="stats-number">{{ total_shared }}</div>
            <div class="stats-label">Shared Studies</div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="stats-card">
            <div class="stats-number">{{ recent_studies|length }}</div>
            <div class="stats-label">Recent Uploads</div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="zap" class="me-2"></i>
                    Quick Actions
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('upload_file') }}" class="btn btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center align-items-center py-3">
                            <i data-feather="upload" class="mb-2" style="width: 32px; height: 32px;"></i>
                            Upload DICOM
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('admin') }}" class="btn btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center align-items-center py-3">
                            <i data-feather="folder" class="mb-2" style="width: 32px; height: 32px;"></i>
                            Manage Files
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="#" onclick="window.open('https://github.com', '_blank')" class="btn btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center align-items-center py-3">
                            <i data-feather="help-circle" class="mb-2" style="width: 32px; height: 32px;"></i>
                            Documentation
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('admin') }}" class="btn btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center align-items-center py-3">
                            <i data-feather="bar-chart-2" class="mb-2" style="width: 32px; height: 32px;"></i>
                            Analytics
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Studies -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="clock" class="me-2"></i>
                    Recent Studies
                </h4>
            </div>
            <div class="card-body">
                {% if recent_studies %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Study</th>
                                    <th>Modality</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                    <th>Views</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for study in recent_studies %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i data-feather="file-text" class="me-2 text-primary"></i>
                                            <div>
                                                <div class="fw-semibold">{{ study.study_description or 'Untitled Study' }}</div>
                                                <small class="text-muted">{{ study.original_filename }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-secondary">{{ study.modality or 'Unknown' }}</span>
                                    </td>
                                    <td>
                                        <div>{{ study.upload_date.strftime('%Y-%m-%d') }}</div>
                                        <small class="text-muted">{{ study.upload_date.strftime('%H:%M') }}</small>
                                    </td>
                                    <td>
                                        {% if study.is_shared %}
                                            <span class="badge badge-success">
                                                <i data-feather="share-2" style="width: 12px; height: 12px;"></i>
                                                Shared
                                            </span>
                                        {% else %}
                                            <span class="badge badge-secondary">Private</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ study.view_count }}</span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('viewer', study_uid=study.study_uid) }}" class="btn btn-outline-primary btn-sm" title="View">
                                                <i data-feather="eye" style="width: 14px; height: 14px;"></i>
                                            </a>
                                            <a href="{{ url_for('share_study', study_uid=study.study_uid) }}" class="btn btn-outline-secondary btn-sm" title="Share">
                                                <i data-feather="share-2" style="width: 14px; height: 14px;"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="text-center mt-3">
                        <a href="{{ url_for('admin') }}" class="btn btn-outline-primary">
                            View All Studies
                            <i data-feather="arrow-right" class="ms-1" style="width: 16px; height: 16px;"></i>
                        </a>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i data-feather="inbox" class="text-muted mb-3" style="width: 64px; height: 64px;"></i>
                        <h4 class="text-muted">No studies uploaded yet</h4>
                        <p class="text-muted mb-4">Upload your first DICOM file to get started</p>
                        <a href="{{ url_for('upload_file') }}" class="btn btn-primary">
                            <i data-feather="upload" class="me-1"></i>
                            Upload DICOM File
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Features Overview -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="star" class="me-2"></i>
                    Platform Features
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="d-flex align-items-start">
                            <div class="flex-shrink-0 me-3">
                                <div class="bg-primary bg-opacity-10 p-2 rounded">
                                    <i data-feather="shield" class="text-primary"></i>
                                </div>
                            </div>
                            <div>
                                <h6>Secure Anonymization</h6>
                                <p class="text-muted mb-0">Automatic removal of patient identifiers for safe sharing</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="d-flex align-items-start">
                            <div class="flex-shrink-0 me-3">
                                <div class="bg-primary bg-opacity-10 p-2 rounded">
                                    <i data-feather="monitor" class="text-primary"></i>
                                </div>
                            </div>
                            <div>
                                <h6>Browser Viewing</h6>
                                <p class="text-muted mb-0">View DICOM images directly in your web browser</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="d-flex align-items-start">
                            <div class="flex-shrink-0 me-3">
                                <div class="bg-primary bg-opacity-10 p-2 rounded">
                                    <i data-feather="share-2" class="text-primary"></i>
                                </div>
                            </div>
                            <div>
                                <h6>Easy Sharing</h6>
                                <p class="text-muted mb-0">Generate shareable links and QR codes for presentations</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
