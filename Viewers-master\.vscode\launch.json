{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "pwa-chrome",
      "request": "launch",
      "name": "Launch Chrome against localhost",
      "url": "http://localhost:3000",
      "webRoot": "${workspaceFolder}"
    }
    // {
    //   "name": "Debug Jest Tests",
    //   "type": "node",
    //   "request": "launch",
    //   "runtimeArgs": [
    //     "--inspect-brk",
    //     "${workspaceRoot}/node_modules/.bin/jest",
    //     "--runInBand"
    //   ],
    //   "console": "integratedTerminal",
    //   "internalConsoleOptions": "neverOpen",
    //   "port": 9229
    // }
  ]
}
