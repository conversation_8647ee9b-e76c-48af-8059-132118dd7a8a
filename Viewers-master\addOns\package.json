{"name": "ohif-monorepo-root", "private": true, "packageManager": "yarn@1.22.22", "workspaces": {"packages": ["../platform/i18n", "../platform/core", "../platform/ui", "../platform/ui-next", "../platform/app", "../extensions/*", "../modes/*", "../addOns/externals/*"], "nohoist": ["**/html-minifier-terser"]}, "scripts": {"preinstall": "cd .. && node preinstall.js"}, "devDependencies": {"@babel/core": "7.24.7", "@babel/plugin-proposal-class-properties": "^7.16.7", "@babel/plugin-proposal-object-rest-spread": "^7.17.3", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-arrow-functions": "^7.16.7", "@babel/plugin-transform-regenerator": "^7.16.7", "@babel/plugin-transform-runtime": "7.24.7", "@babel/plugin-transform-typescript": "^7.13.0", "@babel/preset-env": "7.24.7", "@babel/preset-react": "^7.16.7", "@babel/preset-typescript": "^7.13.0"}, "resolutions": {"**/@babel/runtime": "^7.20.13", "commander": "8.3.0", "dcmjs": "0.38.0", "dicomweb-client": ">=0.10.4", "nth-check": "^2.1.1", "trim-newlines": "^5.0.0", "glob-parent": "^6.0.2", "trim": "^1.0.0", "package-json": "^8.1.0", "typescript": "5.5.4", "sharp": "^0.32.6"}}