{"name": "@ohif/extension-cornerstone", "version": "3.11.0-beta.44", "description": "OHIF extension for Cornerstone", "author": "OHIF", "license": "MIT", "repository": "OHIF/Viewers", "main": "dist/ohif-extension-cornerstone.umd.js", "module": "src/index.tsx", "types": "src/types/index.ts", "exports": {".": "./src/index.tsx", "./types": "./src/types/index.ts"}, "engines": {"node": ">=10", "npm": ">=6", "yarn": ">=1.16.0"}, "files": ["dist", "README.md"], "publishConfig": {"access": "public"}, "scripts": {"clean": "shx rm -rf dist", "clean:deep": "yarn run clean && shx rm -rf node_modules", "dev": "cross-env NODE_ENV=development webpack --config .webpack/webpack.dev.js --watch --output-pathinfo", "dev:cornerstone": "yarn run dev", "build": "cross-env NODE_ENV=production webpack --progress --config .webpack/webpack.prod.js", "build:package-1": "yarn run build", "start": "yarn run dev"}, "peerDependencies": {"@cornerstonejs/codec-charls": "^1.2.3", "@cornerstonejs/codec-libjpeg-turbo-8bit": "^1.2.2", "@cornerstonejs/codec-openjpeg": "^1.2.4", "@cornerstonejs/codec-openjph": "^2.4.5", "@cornerstonejs/dicom-image-loader": "^3.15.6", "@ohif/core": "3.11.0-beta.44", "@ohif/ui": "3.11.0-beta.44", "dcmjs": "*", "dicom-parser": "^1.8.21", "hammerjs": "^2.0.8", "prop-types": "^15.6.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-resize-detector": "^10.0.1"}, "dependencies": {"@babel/runtime": "^7.20.13", "@cornerstonejs/adapters": "^3.15.6", "@cornerstonejs/ai": "^3.15.6", "@cornerstonejs/core": "^3.15.6", "@cornerstonejs/labelmap-interpolation": "^3.15.6", "@cornerstonejs/polymorphic-segmentation": "^3.15.6", "@cornerstonejs/tools": "^3.15.6", "@itk-wasm/morphological-contour-interpolation": "1.1.0", "@kitware/vtk.js": "32.12.0", "html2canvas": "^1.4.1", "lodash.compact": "^3.0.1", "lodash.debounce": "^4.0.8", "lodash.flatten": "^4.4.0", "lodash.merge": "^4.6.2", "lodash.zip": "^4.2.0", "shader-loader": "^1.3.1", "worker-loader": "^3.0.8"}}