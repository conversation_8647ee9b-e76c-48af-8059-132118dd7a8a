import qrcode
import os
import logging
from PIL import Image

def generate_qr_code(url, study_uid):
    """Generate QR code for sharing URL"""
    logger = logging.getLogger(__name__)
    
    try:
        # Create QR code instance
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        
        # Add data
        qr.add_data(url)
        qr.make(fit=True)
        
        # Create image
        img = qr.make_image(fill_color="black", back_color="white")
        
        # Save QR code
        qr_dir = os.path.join(os.getcwd(), 'static', 'qr_codes')
        if not os.path.exists(qr_dir):
            os.makedirs(qr_dir)
        
        qr_filename = f"qr_{study_uid}.png"
        qr_path = os.path.join(qr_dir, qr_filename)
        img.save(qr_path)
        
        logger.info(f"QR code generated: {qr_path}")
        return f"/static/qr_codes/{qr_filename}"
        
    except Exception as e:
        logger.error(f"QR code generation failed: {str(e)}")
        return None
