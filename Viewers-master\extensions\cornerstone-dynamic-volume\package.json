{"name": "@ohif/extension-cornerstone-dynamic-volume", "version": "3.11.0-beta.44", "description": "OHIF extension for 4D volumes data", "author": "OHIF", "license": "MIT", "repository": "OHIF/Viewers", "main": "dist/ohif-extension-cornerstone-dynamic-volume.umd.js", "module": "src/index.ts", "exports": {".": "./src/index.ts", "./types": "./src/types/index.ts"}, "files": ["dist", "README.md"], "publishConfig": {"access": "public"}, "scripts": {"dev": "cross-env NODE_ENV=development webpack --config .webpack/webpack.dev.js --watch --output-pathinfo", "build": "cross-env NODE_ENV=production webpack --config .webpack/webpack.prod.js", "build:package": "yarn run build", "clean": "shx rm -rf dist", "clean:deep": "yarn run clean && shx rm -rf node_modules", "start": "yarn run dev", "test:unit": "jest --watchAll", "test:unit:ci": "jest --ci --runInBand --collectCoverage --passWithNoTests"}, "peerDependencies": {"@ohif/core": "3.11.0-beta.44", "@ohif/extension-cornerstone": "3.11.0-beta.44", "@ohif/extension-default": "3.11.0-beta.44", "@ohif/i18n": "3.11.0-beta.44", "@ohif/ui": "3.11.0-beta.44", "dcmjs": "*", "dicom-parser": "^1.8.21", "hammerjs": "^2.0.8", "prop-types": "^15.6.2", "react": "^18.3.1"}, "dependencies": {"@babel/runtime": "^7.20.13", "@cornerstonejs/core": "^3.15.6", "@cornerstonejs/tools": "^3.15.6", "classnames": "^2.3.2"}}