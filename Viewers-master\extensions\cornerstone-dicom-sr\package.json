{"name": "@ohif/extension-cornerstone-dicom-sr", "version": "3.11.0-beta.44", "description": "OHIF extension for an SR Cornerstone Viewport", "author": "OHIF", "license": "MIT", "repository": "OHIF/Viewers", "main": "dist/ohif-extension-cornerstone-dicom-sr.umd.js", "module": "src/index.tsx", "engines": {"node": ">=14", "npm": ">=6", "yarn": ">=1.16.0"}, "files": ["dist", "README.md"], "publishConfig": {"access": "public"}, "keywords": ["ohif-extension"], "scripts": {"clean": "shx rm -rf dist", "clean:deep": "yarn run clean && shx rm -rf node_modules", "dev": "cross-env NODE_ENV=development webpack --config .webpack/webpack.dev.js --watch --output-pathinfo", "dev:cornerstone": "yarn run dev", "build": "cross-env NODE_ENV=production webpack --config .webpack/webpack.prod.js", "build:package-1": "yarn run build", "start": "yarn run dev", "test:unit": "jest --watchAll", "test:unit:ci": "jest --ci --runInBand --collectCoverage --passWithNoTests"}, "peerDependencies": {"@ohif/core": "3.11.0-beta.44", "@ohif/extension-cornerstone": "3.11.0-beta.44", "@ohif/extension-measurement-tracking": "3.11.0-beta.44", "@ohif/ui": "3.11.0-beta.44", "dcmjs": "*", "dicom-parser": "^1.8.9", "hammerjs": "^2.0.8", "prop-types": "^15.6.2", "react": "^18.3.1"}, "dependencies": {"@babel/runtime": "^7.20.13", "@cornerstonejs/adapters": "^3.15.6", "@cornerstonejs/core": "^3.15.6", "@cornerstonejs/tools": "^3.15.6", "classnames": "^2.3.2"}}