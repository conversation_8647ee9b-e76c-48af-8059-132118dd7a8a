// Initialize Cornerstone
cornerstoneWADOImageLoader.external.cornerstone = cornerstone;
cornerstoneWADOImageLoader.external.dicomParser = dicomParser;
cornerstoneTools.external.cornerstone = cornerstone;
cornerstoneTools.external.cornerstoneMath = cornerstoneMath;
cornerstoneTools.init();

class DicomViewer {
    constructor(elementId, studyUid) {
        this.element = document.getElementById(elementId);
        this.studyUid = studyUid;
        this.toolState = 'none';
        this.imageId = null;
        
        this.init();
    }
    
    async init() {
        // Enable the element for Cornerstone
        cornerstone.enable(this.element);
        
        // Initialize tools
        this.initializeTools();
        
        // Load the DICOM image
        await this.loadImage();
    }
    
    initializeTools() {
        // Initialize tools
        cornerstoneTools.addTool(cornerstoneTools.WwwcTool);
        cornerstoneTools.addTool(cornerstoneTools.PanTool);
        cornerstoneTools.addTool(cornerstoneTools.ZoomTool);
        cornerstoneTools.addTool(cornerstoneTools.LengthTool);
        cornerstoneTools.addTool(cornerstoneTools.AngleTool);
        
        // Set the default tool
        cornerstoneTools.setToolActive('Wwwc', { mouseButtonMask: 1 });
    }
    
    async loadImage() {
        try {
            // Configure cornerstone for direct loading
            const baseUrl = window.location.origin;
            const url = `${baseUrl}/api/dicom-data/${this.studyUid}`;
            
            // Fetch the DICOM file directly
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            // Get the file as ArrayBuffer
            const dicomData = await response.arrayBuffer();
            
            // Create a Blob URL
            const blob = new Blob([dicomData], { type: 'application/dicom' });
            const blobUrl = URL.createObjectURL(blob);
            
            // Load the image using cornerstone
            this.imageId = `dicomfile:${blobUrl}`;
            
            // Register the blob loader
            cornerstone.registerImageLoader('dicomfile', (imageId) => {
                const url = imageId.substring(10);
                const imagePixelModule = cornerstoneWADOImageLoader.wadouri.getImagePixelData(dicomData);
                
                return {
                    promise: new Promise((resolve) => {
                        resolve({
                            imageId: imageId,
                            minPixelValue: imagePixelModule.minPixelValue,
                            maxPixelValue: imagePixelModule.maxPixelValue,
                            slope: imagePixelModule.slope,
                            intercept: imagePixelModule.intercept,
                            windowCenter: imagePixelModule.windowCenter,
                            windowWidth: imagePixelModule.windowWidth,
                            getPixelData: () => imagePixelModule.pixelData,
                            rows: imagePixelModule.rows,
                            columns: imagePixelModule.columns,
                            height: imagePixelModule.rows,
                            width: imagePixelModule.columns,
                            color: false,
                            columnPixelSpacing: imagePixelModule.columnPixelSpacing,
                            rowPixelSpacing: imagePixelModule.rowPixelSpacing,
                            sizeInBytes: dicomData.byteLength
                        });
                    })
                };
            });
            
            // Load and display the image
            const image = await cornerstone.loadAndCacheImage(this.imageId);
            cornerstone.displayImage(this.element, image);
            
            // Add the loaded image to the tool state
            cornerstoneTools.addStackStateManager(this.element, ['stack']);
            
        } catch (error) {
            console.error('Error loading DICOM image:', error);
            this.showError(`Failed to load DICOM image: ${error.message}`);
        }
    }
    
    resetImage() {
        cornerstone.reset(this.element);
    }
    
    toggleWindowLevel() {
        this.setActiveTool('Wwwc');
    }
    
    togglePan() {
        this.setActiveTool('Pan');
    }
    
    toggleZoom() {
        this.setActiveTool('Zoom');
    }
    
    invertImage() {
        const viewport = cornerstone.getViewport(this.element);
        viewport.invert = !viewport.invert;
        cornerstone.setViewport(this.element, viewport);
    }
    
    setActiveTool(toolName) {
        if (this.toolState === toolName) {
            // Disable the tool if it's already active
            cornerstoneTools.setToolDisabled(toolName);
            this.toolState = 'none';
        } else {
            // Enable the new tool
            cornerstoneTools.setToolActive(toolName, { mouseButtonMask: 1 });
            this.toolState = toolName;
        }
    }
    
    showError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-danger m-3';
        errorDiv.textContent = message;
        this.element.appendChild(errorDiv);
    }
}

// Initialize viewer when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    const element = document.getElementById('dicom-image');
    if (element) {
        const studyUid = element.getAttribute('data-study-uid');
        if (studyUid) {
            window.viewer = new DicomViewer('dicom-image', studyUid);
        }
    }
});
