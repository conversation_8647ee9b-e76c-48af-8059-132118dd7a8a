// OHIF Viewer Integration
window.config = {
    // Default OHIF Viewer configuration
    routerBasename: '/',
    servers: {
        dicomWeb: [
            {
                name: 'MedShare DICOM',
                wadoUriRoot: '/api/dicom-data',
                qidoRoot: '/api/studies',
                wadoRoot: '/api/studies',
                qidoSupportsIncludeField: false,
                imageRendering: 'wadors',
                thumbnailRendering: 'wadors',
                enableStudyLazyLoad: true,
            },
        ],
    },
    // Study list configuration
    studyListFunctionsEnabled: true,
    maxConcurrentMetadataRequests: 5,
    maxConcurrentRequestsToServer: 5,
};

// Initialize OHIF Viewer
function initOHIFViewer(studyInstanceUID) {
    const frame = document.getElementById('ohif-viewer-frame');
    if (frame) {
        // Add event listener for viewer loaded
        frame.addEventListener('load', function() {
            console.log('OHIF Viewer loaded');
        });

        // Add event listener for viewer errors
        frame.addEventListener('error', function(e) {
            console.error('Error loading OHIF Viewer:', e);
        });
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    const container = document.getElementById('ohif-viewer-container');
    if (container) {
        const studyUID = container.getAttribute('data-study-uid');
        if (studyUID) {
            initOHIFViewer(studyUID);
        }
    }
});
