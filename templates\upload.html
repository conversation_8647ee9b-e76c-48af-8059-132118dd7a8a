{% extends "base.html" %}

{% block title %}Upload DICOM - DICOM Platform{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h3 class="mb-0">
                    <i data-feather="upload" class="me-2"></i>
                    Upload DICOM File
                </h3>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="upload-form">
                    <div class="mb-4">
                        <label class="form-label fw-semibold">Select DICOM File</label>
                        <div class="upload-area" id="upload-area">
                            <div class="upload-icon">
                                <i data-feather="upload-cloud"></i>
                            </div>
                            <h4>Drag & drop your DICOM file here</h4>
                            <p class="text-muted">or click to browse files</p>
                            <p class="text-muted mb-0">
                                <small>Supported formats: .dcm, .dicom, .zip (max 500MB)</small>
                            </p>
                        </div>
                        <input type="file" 
                               name="file" 
                               id="file-input" 
                               accept=".dcm,.dicom,.zip" 
                               style="display: none;" 
                               required>
                    </div>
                    
                    <!-- Upload Progress -->
                    <div class="progress mb-3" style="display: none;" id="upload-progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" 
                             style="width: 0%" 
                             aria-valuenow="0" 
                             aria-valuemin="0" 
                             aria-valuemax="100">0%</div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('index') }}" class="btn btn-secondary me-md-2">
                            <i data-feather="arrow-left" class="me-1"></i>
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary" id="submit-btn">
                            <i data-feather="upload" class="me-1"></i>
                            Upload & Anonymize
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Information Card -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i data-feather="info" class="me-2"></i>
                    Important Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">
                            <i data-feather="shield" class="me-1"></i>
                            Privacy & Security
                        </h6>
                        <ul class="list-unstyled">
                            <li><i data-feather="check" class="text-success me-1" style="width: 16px; height: 16px;"></i> Automatic patient data anonymization</li>
                            <li><i data-feather="check" class="text-success me-1" style="width: 16px; height: 16px;"></i> Secure file storage</li>
                            <li><i data-feather="check" class="text-success me-1" style="width: 16px; height: 16px;"></i> HIPAA compliant processing</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">
                            <i data-feather="file-text" class="me-1"></i>
                            Supported Features
                        </h6>
                        <ul class="list-unstyled">
                            <li><i data-feather="check" class="text-success me-1" style="width: 16px; height: 16px;"></i> DICOM metadata extraction</li>
                            <li><i data-feather="check" class="text-success me-1" style="width: 16px; height: 16px;"></i> Browser-based viewing</li>
                            <li><i data-feather="check" class="text-success me-1" style="width: 16px; height: 16px;"></i> Shareable links & QR codes</li>
                        </ul>
                    </div>
                </div>
                
                <div class="alert alert-info mt-3">
                    <i data-feather="alert-circle" class="me-2"></i>
                    <strong>Processing Note:</strong> 
                    Upon upload, your DICOM file will be automatically anonymized by removing patient identifiers 
                    while preserving the medical imaging data and relevant metadata for clinical review.
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('upload-form').addEventListener('submit', function(e) {
    const fileInput = document.getElementById('file-input');
    const submitBtn = document.getElementById('submit-btn');
    const progressBar = document.getElementById('upload-progress');
    
    if (!fileInput.files || fileInput.files.length === 0) {
        e.preventDefault();
        alert('Please select a file to upload.');
        return;
    }
    
    // Show progress bar and disable submit button
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i data-feather="loader" class="me-1"></i> Processing...';
    progressBar.style.display = 'block';
    
    // Simulate progress (since we can't track actual upload progress easily)
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 90) progress = 90;
        
        const progressBarElement = progressBar.querySelector('.progress-bar');
        progressBarElement.style.width = progress + '%';
        progressBarElement.textContent = Math.round(progress) + '%';
        progressBarElement.setAttribute('aria-valuenow', progress);
        
        if (progress >= 90) {
            clearInterval(interval);
        }
    }, 200);
});
</script>
{% endblock %}
