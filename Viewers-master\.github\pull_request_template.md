<!-- Do Not Delete This! pr_template -->
<!-- Please read our Rules of Conduct: https://github.com/OHIF/Viewers/blob/master/CODE_OF_CONDUCT.md -->
<!-- 🕮 Read our guide about our Contributing Guide here https://docs.ohif.org/development/contributing -->
<!-- :hand: Thank you for starting this amazing contribution! -->

<!--
⚠️⚠️ Please make sure the checklist section below is complete before submitting your PR.
To complete the checklist, add an 'x' to each item: [] -> [x]
(PRs that do not have all the checkboxes marked will not be approved)
-->

### Context

<!--
Provide a clear explanation of the reasoning behind this change, such as:
- A link to the issue being addressed, using the format "Fixes #ISSUE_NUMBER"
- An image showing the issue or problem being addressed (if not already in the issue)
- Error logs or callStacks to help with the understanding of the problem (if not already in the issue)
-->

### Changes & Results

<!--
List all the changes that have been done, such as:
- Add new components
- Remove old components
- Update dependencies

What are the effects of this change?
- Before vs After
- Screenshots / GIFs / Videos
-->

### Testing

<!--
Describe how we can test your changes.
- open a URL
- visit a page
- click on a button
- etc.
-->

### Checklist

#### PR

<!--
https://semantic-release.gitbook.io/semantic-release/#how-does-it-work

Examples:
Please note the letter casing in the provided examples (upper or lower).

- feat(MeasurementService): add ...
- fix(Toolbar): fix ...
- docs(Readme): update ...
- style(Whitespace): fix ...
- refactor(ExtensionManager): ...
- test(HangingProtocol): Add test ...
- chore(git): update ...
- perf(VolumeLoader): ...

You don't need to have each commit within the Pull Request follow the rule,
but the PR title must comply with it, as it will be used as the commit message
after the commits are squashed.
-->

- [] My Pull Request title is descriptive, accurate and follows the
  semantic-release format and guidelines.

#### Code

- [] My code has been well-documented (function documentation, inline comments,
  etc.)

#### Public Documentation Updates

<!-- https://docs.ohif.org/ -->

- [] The documentation page has been updated as necessary for any public API
  additions or removals.

#### Tested Environment

- [] OS: <!--[e.g. Windows 10, macOS 10.15.4]-->
- [] Node version: <!--[e.g. 18.16.1]-->
- [] Browser:
  <!--[e.g. Chrome 83.0.4103.116, Firefox 77.0.1, Safari 13.1.1]-->

<!-- prettier-ignore-start -->
[blog]: https://circleci.com/blog/triggering-trusted-ci-jobs-on-untrusted-forks/
[script]: https://github.com/jklukas/git-push-fork-to-upstream-branch
<!-- prettier-ignore-end -->
