name: Playwright Tests
on:
  pull_request:
    branches: [master]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  playwright-tests:
    timeout-minutes: 60
    runs-on: self-hosted
    strategy:
      fail-fast: false
      matrix:
        node-version: [20]
    steps:
      - uses: actions/checkout@v4
      - uses: oven-sh/setup-bun@v2
      - uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
      - name: Install dependencies
        run: bun install --frozen-lockfile
      - name: Install Playwright browsers
        run: npx playwright install
      - name: Run Playwright tests
        run: |
          export NODE_OPTIONS="--max_old_space_size=10192"
          bun run test:e2e:coverage
      - name: create the coverage report
        run: |
          bun nyc report --reporter=lcov --reporter=text
      - name: Upload the coverage report to GitHub Actions Artifacts
        if: ${{ !cancelled() }}
        uses: actions/upload-artifact@v4
        with:
          name: coverage-report-pr
          path: coverage
          retention-days: 3
