{
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.rulers": [80, 120],
  // ===
  // Spacing
  // ===
  "editor.insertSpaces": true,
  "editor.tabSize": 2,
  "editor.trimAutoWhitespace": true,
  "files.trimTrailingWhitespace": true,
  "files.eol": "\n",
  "files.insertFinalNewline": true,
  "files.trimFinalNewlines": true,
  // ===
  // Event Triggers
  // ===
  "editor.formatOnSave": true,
  "eslint.run": "onSave",
  "jest.autoRun": "off",
  "prettier.disableLanguages": ["html"],
  "prettier.endOfLine": "lf",
  "workbench.colorCustomizations": {},
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "cSpell.userWords": [
    "aabb",
    "architectured",
    "attrname",
    "Barksy",
    "browserslist",
    "bulkdata",
    "Cacheable",
    "cfun",
    "clonedeep",
    "Colormap",
    "Colormaps",
    "Comlink",
    "cornerstonejs",
    "Crosshairs",
    "datasource",
    "dcmjs",
    "decache",
    "decached",
    "decaching",
    "deepmerge",
    "Dicom",
    "dicomweb",
    "DISPLAYSETS",
    "glwindow",
    "grababble",
    "grabbable",
    "Hounsfield",
    "Interactable",
    "Interactor",
    "istyle",
    "kitware",
    "labelmap",
    "labelmaps",
    "livewire",
    "Mergeable",
    "multiframe",
    "nifti",
    "ofun",
    "OHIF",
    "polylines",
    "POLYSEG",
    "prapogation",
    "precisionmetrics",
    "prefetch",
    "Prescaled",
    "pydicom",
    "Radiopharmaceutical",
    "rasterizing",
    "reconstructable",
    "Rehydratable",
    "renderable",
    "resampler",
    "resemblejs",
    "reslice",
    "resliced",
    "Reslices",
    "roadmap",
    "ROADMAPS",
    "Segmentations",
    "semibold",
    "sitk",
    "SUBRESOLUTION",
    "suvbsa",
    "suvbw",
    "suvlbm",
    "textbox",
    "thresholded",
    "thresholding",
    "timepoint",
    "timepoints",
    "TMTV",
    "TOOLGROUP",
    "tqdm",
    "transferables",
    "typedoc",
    "unsubscriptions",
    "uuidv",
    "viewplane",
    "viewports",
    "Voxel",
    "Voxels",
    "Vtkjs",
    "wado",
    "wadors",
    "wadouri",
    "workerpool",
    "Colorbar",
    "Colorbars"
  ]
}
